# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Request handling classes and functionality. """

import logging
import werkzeug.wrappers

import odoo
from ..tools._vendor.useragents import UserAgent
from ..modules.registry import Registry
from .utils import get_default_session, db_filter, db_list
from .geoip import GeoIP
from .response import FutureResponse

_logger = logging.getLogger(__name__)

# Default max content length
DEFAULT_MAX_CONTENT_LENGTH = 128 * 1024 * 1024  # 128MiB


def make_request_wrap_methods(attr):
    def getter(self):
        return getattr(self._HTTPRequest__wrapped, attr)

    def setter(self, value):
        return setattr(self._HTTPRequest__wrapped, attr, value)

    return getter, setter


HTTPREQUEST_ATTRIBUTES = [
    '__str__', '__repr__', '__exit__',
    'accept_charsets', 'accept_languages', 'accept_mimetypes', 'access_route', 'args', 'authorization', 'base_url',
    'charset', 'content_encoding', 'content_length', 'content_md5', 'content_type', 'cookies', 'data', 'date',
    'encoding_errors', 'files', 'form', 'full_path', 'get_data', 'get_json', 'headers', 'host', 'host_url', 'if_match',
    'if_modified_since', 'if_none_match', 'if_range', 'if_unmodified_since', 'is_json', 'is_secure', 'json',
    'max_content_length', 'method', 'mimetype', 'mimetype_params', 'origin', 'path', 'pragma', 'query_string', 'range',
    'referrer', 'remote_addr', 'remote_user', 'root_path', 'root_url', 'scheme', 'script_root', 'server', 'session',
    'trusted_hosts', 'url', 'url_charset', 'url_root', 'user_agent', 'values',
]


class HTTPRequest:
    def __init__(self, environ):
        httprequest = werkzeug.wrappers.Request(environ)
        httprequest.user_agent_class = UserAgent  # use vendored userAgent since it will be removed in 2.1
        httprequest.parameter_storage_class = werkzeug.datastructures.ImmutableMultiDict
        httprequest.max_content_length = DEFAULT_MAX_CONTENT_LENGTH

        # Monkey-patch the httprequest to add session_id property
        httprequest._session_id__ = httprequest.cookies.get('session_id')

        self.httprequest = httprequest

    def __getattr__(self, name):
        return getattr(self.httprequest, name)


class Request:
    """
    Wrapper around the incoming HTTP request with deserialized request
    parameters, session utilities and request dispatching logic.
    """

    def __init__(self, httprequest):
        self.httprequest = httprequest
        self.future_response = FutureResponse()
        # Import here to avoid circular imports
        from .dispatcher import _dispatchers
        self.dispatcher = _dispatchers['http'](self)  # until we match
        #self.params = {}  # set by the Dispatcher

        self.geoip = GeoIP(httprequest.remote_addr)
        self.registry = None
        self.env = None

    def _post_init(self):
        self.session, self.db = self._get_session_and_dbname()
        self._post_init = None

    def _get_session_and_dbname(self):
        # Import here to avoid circular imports
        from . import root
        
        sid = self.httprequest._session_id__
        if not sid or not root.session_store.is_valid_key(sid):
            session = root.session_store.new()
        else:
            session = root.session_store.get(sid)
            session.sid = sid  # in case the session was not persisted

        for key, val in get_default_session().items():
            session.setdefault(key, val)
        if not session.context.get('lang'):
            session.context['lang'] = self.default_lang()

        dbname = None
        host = self.httprequest.environ['HTTP_HOST']
        if session.db and db_filter([session.db], host=host):
            dbname = session.db
        else:
            all_dbs = db_list(force=True, host=host)
            if len(all_dbs) == 1:
                dbname = all_dbs[0]  # monodb

        if session.db != dbname:
            if session.db:
                _logger.warning("Logged into database %r, but dbfilter rejects it; logging session out.", session.db)
                session.logout(keep_db=False)
            session.db = dbname

        session.is_dirty = False
        return session, dbname

    def _open_registry(self):
        try:
            registry = Registry(self.db)
            # use a RW cursor! Sequence data is not replicated and would
            # be invalid if accessed on a readonly replica. Cfr task-4399456
            cr_readwrite = registry.cursor(readonly=False)
            self.registry = registry
            self.env = odoo.api.Environment(cr_readwrite, self.session.uid, self.session.context)
        except Exception:
            _logger.exception("Failed to open registry for database %r", self.db)
            raise

    def default_lang(self):
        """
        Return the default language for the current request.
        """
        # Import here to avoid circular imports
        from . import DEFAULT_LANG
        
        # Try to get language from Accept-Language header
        if self.httprequest.accept_languages:
            for lang in self.httprequest.accept_languages:
                if '_' in lang[0]:
                    return lang[0]
        return DEFAULT_LANG

    def csrf_token(self, time_limit=None):
        """
        Generate a CSRF token for the current session.
        This is a simplified implementation.
        """
        import hashlib
        import time
        
        if not hasattr(self, 'session') or not self.session:
            return None
            
        # Simple token generation based on session and timestamp
        timestamp = str(int(time.time() // 3600))  # Hour-based token
        token_data = f"{self.session.sid}:{timestamp}"
        return hashlib.sha256(token_data.encode()).hexdigest()[:32]

    def validate_csrf(self, csrf):
        """
        Is the given csrf token valid ?

        :param str csrf: The token to validate.
        :returns: ``True`` when valid, ``False`` when not.
        """
        if not csrf:
            return False
        return csrf == self.csrf_token()

    def make_response(self, data, headers=None, cookies=None, status=200):
        """
        Create a response object.
        """
        # Import here to avoid circular imports
        from .response import Response
        
        response = Response(data, status=status, headers=headers)
        if cookies:
            for name, value in cookies.items():
                response.set_cookie(name, value)
        return response

    def make_json_response(self, data, headers=None, cookies=None, status=200):
        """ Helper for JSON responses, it json-serializes ``data`` and
        sets the Content-Type header accordingly if none is provided.

        :param data: the data that will be json-serialized into the response body
        :param int status: http status code
        :param headers: HTTP headers to set on the response
        :param cookies: cookies to set on the response
        """
        import json
        from .response import Response

        json_data = json.dumps(data, default=str)
        headers = headers or {}
        headers['Content-Type'] = 'application/json'
        response = Response(json_data, status=status, headers=headers)
        if cookies:
            for k, v in cookies.items():
                response.set_cookie(k, v)
        return response

    def redirect(self, location, code=303, local=True):
        """
        Create a redirect response.

        :param location: the location to redirect to
        :param code: the redirect status code (default: 303)
        :param local: whether to ensure the redirect is local (default: True)
        """
        from werkzeug.utils import redirect as werkzeug_redirect
        from ..tools.misc import url_parse
        from .response import Response

        # compatibility, Werkzeug support URL as location
        if hasattr(location, 'to_url'):  # URL object
            location = location.to_url()
        if local:
            location = '/' + url_parse(location).replace(scheme='', netloc='').to_url().lstrip('/\\')

        return werkzeug_redirect(location, code, Response=Response)

    def not_found(self, description=None):
        """
        Create a 404 Not Found response.
        """
        from werkzeug.exceptions import NotFound
        raise NotFound(description)

    def render(self, template, qcontext=None, lazy=True, **kw):
        """
        Render a QWeb template.
        This is a simplified implementation.
        """
        # This would normally render a QWeb template
        # For now, return a simple response
        return self.make_response(f"Template: {template}")

    def _serve_static(self):
        """
        Serve static files.
        This is a simplified implementation.
        """
        # This would normally serve static files from the filesystem
        return self.not_found("Static file not found")

    def _serve_nodb(self):
        """
        Handle requests when no database is available.
        This is a simplified implementation.
        """
        # This would normally handle auth='none' routes
        return self.not_found("No database available")

    def _serve_db(self):
        """
        Handle requests with database access.
        This is a simplified implementation.
        """
        # This would normally handle database-enabled routes
        self._open_registry()
        # Delegate to ir.http model
        return self.env['ir.http']._serve_fallback()

    def _transactioning(self, func):
        """
        Execute function within a database transaction.
        This is a simplified implementation.
        """
        try:
            return func()
        except Exception as e:
            if self.env and self.env.cr:
                self.env.cr.rollback()
            raise
