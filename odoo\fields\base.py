# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Base field classes and metaclass. """
from __future__ import annotations

import itertools
import typing

from ..tools.misc import Sentinel, SENTINEL
from ..tools import lazy_property

from odoo.api import ContextType, DomainType, IdType, NewId, M, T
from odoo.exceptions import AccessError


class MetaField(type):
    """ Metaclass for field classes. """
    by_type = {}

    def __init__(cls, name, bases, attrs):
        super(MetaField, cls).__init__(name, bases, attrs)
        if not hasattr(cls, 'type'):
            return

        if cls.type and cls.type not in MetaField.by_type:
            MetaField.by_type[cls.type] = cls

        # compute class attributes to avoid calling dir() on fields
        cls.related_attrs = []
        cls.description_attrs = []
        for attr in dir(cls):
            if attr.startswith('_related_'):
                cls.related_attrs.append((attr[9:], attr))
            elif attr.startswith('_description_'):
                cls.description_attrs.append((attr[13:], attr))


_global_seq = iter(itertools.count())


class Field(MetaField('DummyField', (object,), {}), typing.Generic[T]):
    """The field descriptor contains the field definition, and manages accesses
    and assignments of the corresponding field on records. The following
    attributes may be provided when instantiating a field:

    :param str string: the label of the field seen by users; if not
        set, the ORM takes the field name in the class (capitalized).

    :param str help: the tooltip of the field seen by users

    :param bool readonly: whether the field is readonly (default: ``False``)

        This only has an impact on the UI. Any field assignation in code will work
        (if the field is a stored field or an inversable one).

    :param bool required: whether the value of the field is required (default: ``False``)

    :param str index: whether the field is indexed in database, and the kind of index.
        Note: this has no effect on non-stored and virtual fields.
        The possible values are:

        * ``"btree"`` or ``True``: standard index, good for many2one
        * ``"btree_not_null"``: BTREE index without NULL values (useful when most
                                values are NULL, or when NULL is never searched for)
        * ``"trigram"``: Generalized Inverted Index (GIN) with trigrams (good for full-text search)
        * ``None`` or ``False``: no index (default)

    :param default: the default value for the field; this is either a static
        value, or a function taking a recordset and returning a value; use
        ``default=None`` to discard default values for the field
    :type default: value or callable

    :param str groups: comma-separated list of group xml ids (string); this
        restricts the field access to the users of the given groups only

    :param bool company_dependent: whether the field value is dependent of the current company;

        The value is stored on the model table as jsonb dict with the company id as the key.

        The field's default values stored in model ir.default are used as fallbacks for
        unspecified values in the jsonb dict.

    :param bool copy: whether the field value should be copied when the record
        is duplicated (default: ``True`` for normal fields, ``False`` for
        ``one2many`` and computed fields, including property fields and
        related fields)

    :param bool store: whether the field is stored in database
        (default:``True``, ``False`` for computed fields)

    :param str aggregator: aggregate function used by :meth:`~odoo.models.Model.read_group`
        when grouping on this field.

        Supported aggregate functions are:

        * ``array_agg`` : values, including nulls, concatenated into an array
        * ``count`` : number of rows
        * ``count_distinct`` : number of distinct rows
        * ``bool_and`` : true if all values are true, otherwise false
        * ``bool_or`` : true if at least one value is true, otherwise false
        * ``max`` : maximum value of all values
        * ``min`` : minimum value of all values
        * ``avg`` : the average (arithmetic mean) of all values
        * ``sum`` : sum of all values

    :param str group_expand: function used to expand read_group results when grouping on
        the current field. For selection fields, ``group_expand=True`` automatically
        expands groups for all selection keys.

        .. code-block:: python

            @api.model
            def _read_group_selection_field(self, values, domain):
                return ['choice1', 'choice2', ...] # available selection choices.

            @api.model
            def _read_group_many2one_field(self, records, domain):
                return records + self.search([custom_domain])

    .. rubric:: Computed Fields

    :param str compute: name of a method that computes the field

        .. seealso:: :ref:`Advanced Fields/Compute fields <reference/fields/compute>`

    :param bool precompute: whether the field should be computed before record insertion
        in database.  Should be used to specify manually some fields as precompute=True
        when the field can be computed before record insertion.
        (e.g. avoid statistics fields based on search/read_group), many2one
        linking to the previous record, ... (default: `False`)

        .. warning::

            Precomputation only happens when no explicit value and no default
            value is provided to create().  This means that a default value
            disables the precomputation, even if the field is specified as
            precompute=True.

            Precomputing a field can be counterproductive if the records of the
            given model are not created in batch.  Consider the situation were
            many records are created one by one.  If the field is not
            precomputed, it will normally be computed in batch at the flush(),
            and the prefetching mechanism will help making the computation
            efficient.  On the other hand, if the field is precomputed, the
            computation will be made one by one, and will therefore not be able
            to take advantage of the prefetching mechanism.

            Following the remark above, precomputed fields can be interesting on
            the lines of a one2many, which are usually created in batch by the
            ORM itself, provided that they are created by writing on the record
            that contains them.

    :param bool compute_sudo: whether the field should be recomputed as superuser
        to bypass access rights (by default ``True`` for stored fields, ``False``
        for non stored fields)

    :param bool recursive: whether the field has recursive dependencies (the field
        ``X`` has a dependency like ``parent_id.X``); declaring a field recursive
        must be explicit to guarantee that recomputation is correct

    :param str inverse: name of a method that inverses the field (optional)

    :param str search: name of a method that implement search on the field (optional)

    :param str related: sequence of field names

    :param bool default_export_compatible: whether the field must be exported by default in an import-compatible export

        .. seealso:: :ref:`Advanced fields/Related fields <reference/fields/related>`
    """

    type: str                           # type of the field (string)
    relational = False                  # whether the field is a relational one
    translate = False                   # whether the field is translated

    write_sequence = 0  # field ordering for write()
    # Database column type (ident, spec) for non-company-dependent fields.
    # Company-dependent fields are stored as jsonb (see column_type).
    _column_type: typing.Tuple[str, str] | None = None

    _args__ = None                      # the parameters given to __init__()
    _module = None                      # the field's module name
    _modules = None                     # modules that define this field
    _setup_done = True                  # whether the field is completely set up
    _sequence = None                    # absolute ordering of the field
    _base_fields = ()                   # the fields defining self, in override order
    _extra_keys = ()                    # unknown attributes set on the field
    _direct = False                     # whether self may be used directly (shared)
    _toplevel = False                   # whether self is on the model's registry class

    automatic = False                   # whether the field is automatically created ("magic" field)
    inherited = False                   # whether the field is inherited (_inherits)
    inherited_field = None              # the corresponding inherited field

    name: str                           # name of the field
    model_name: str | None = None       # name of the model of this field
    comodel_name: str | None = None     # name of the model of values (if relational)

    string: str | None = None           # field label
    help: str | None = None             # field tooltip
    readonly = False                    # whether the field is readonly
    required = False                    # whether the field is required
    index = None                        # whether the field is indexed, and how
    default = None                      # default value
    groups = None                       # csv list of group xml ids
    company_dependent = False           # whether the field is company-dependent
    copy = True                         # whether the field is copied over by BaseModel.copy()
    _depends = None                     # collection of field dependencies
    _depends_context = None             # collection of context key dependencies
    recursive = False                   # whether self depends on itself

    store = True                        # whether the field is stored in database
    index = None                        # how the field is indexed in database
    manual = False                      # whether the field is a custom field
    copy = True                         # whether the field is copied over by BaseModel.copy()
    _depends = None                     # collection of field dependencies
    _depends_context = None             # collection of context key dependencies
    recursive = False                   # whether self depends on itself

    compute = None                      # compute method (name or callable)
    compute_sudo = None                 # whether to compute the field as admin
    inverse = None                      # inverse method (name or callable)
    search = None                       # search method (name or callable)
    related = None                      # sequence of field names
    default_export_compatible = True    # whether the field is exported by default

    aggregator = None                   # aggregate function for read_group
    group_expand = None                 # group expand function for read_group
    prefetch = True                     # whether the field is prefetched
    precompute = False                  # whether the field is precomputed
    context_dependent = False           # whether the field depends on context

    # properties for company-dependent fields
    _related_company_dependent = None
    _description_company_dependent = None

    def __init__(self, string: str | Sentinel = SENTINEL, **kwargs):
        kwargs['string'] = string
        args = {key: val for key, val in kwargs.items() if val is not SENTINEL}
        self._sequence = next(_global_seq)
        self._args__ = args
        self._setup_done = False

    def __str__(self):
        return f"{self.model_name}.{self.name}"

    def __repr__(self):
        return f"<field '{self.model_name}.{self.name}'>"

    #
    # Base field setup: things that do not depend on other models/fields
    #
    # The base field setup is done by field.__set_name__(), which determines the
    # field's name, model name, module and its parameters.
    #
    # The dictionary field._args__ gives the parameters passed to the field's
    # constructor.  Most parameters have an attribute of the same name on the
    # field.  The parameters as attributes are assigned by the field setup.
    #
    # When several definition classes of the same model redefine a given field,
    # the field occurrences are "merged" into one new field instantiated at
    # runtime on the registry class of the model.  The occurrences of the field
    # are given to the new field as the parameter '_base_fields'; it is a list
    # of fields in override order (or reverse MRO).
    #
    # In order to save memory, a field should avoid having field._args__ and/or
    # many attributes when possible.  We call "direct" a field that can be set
    # up directly from its definition class.  Direct fields are non-related
    # fields defined on models, and can be shared across registries.  We call
    # "toplevel" a field that is put on the model's registry class, and is
    # therefore specific to the registry.
    #
    # Toplevel field are set up once, and are no longer set up from scratch
    # after that.  Those fields can save memory by discarding field._args__ and
    # field._base_fields once set up, because those are no longer necessary.
    #
    # Non-toplevel non-direct fields are the fields on definition classes that
    # may not be shared.  In other words, those fields are never used directly,
    # and are always recreated as toplevel fields.  On those fields, the base
    # setup is useless, because only field._args__ is used for setting up other
    # fields.  We therefore skip the base setup for those fields.  The only
    # attributes of those fields are: '_sequence', '_args__', 'model_name', 'name'
    # and '_module', which makes their __dict__'s size minimal.

    def __set_name__(self, owner, name):
        """ Perform the base setup of a field.

        :param owner: the owner class of the field (the model's definition or registry class)
        :param name: the name of the field
        """
        # Import here to avoid circular imports
        from ..models import BaseModel
        from ..models.utils import is_definition_class

        assert issubclass(owner, BaseModel)
        self.model_name = owner._name
        self.name = name
        if is_definition_class(owner):
            # only for fields on definition classes, not registry classes
            self._module = owner._module
            owner._field_definitions.append(self)

        if not self._args__.get('related'):
            self._direct = True
        if self._direct or self._toplevel:
            self._setup_attrs(owner, name)
            if self._toplevel:
                # free memory, self._args__ and self._base_fields are no longer useful
                self.__dict__.pop('_args__', None)
                self.__dict__.pop('_base_fields', None)

    #
    # Setup field parameter attributes
    #

    def _get_attrs(self, model_class, name):
        """ Return the field parameter attributes as a dictionary. """
        # determine all inherited field attributes
        attrs = {}
        modules = []
        for field in self._args__.get('_base_fields', ()):
            if not isinstance(self, type(field)):
                # 'self' overrides 'field' and their types are not compatible;
                # so we ignore all the parameters collected so far
                attrs.clear()
                modules.clear()
                continue
            attrs.update(field._args__)
            if field._module:
                modules.append(field._module)
        attrs.update(self._args__)
        if self._module:
            modules.append(self._module)

        attrs['_args__'] = dict(self._args__)
        attrs['model_name'] = model_class._name
        attrs['name'] = name
        attrs['_module'] = modules[-1] if modules else None
        attrs['_modules'] = tuple(set(modules))

        # initialize ``self`` with ``attrs``
        if name == 'state':
            # by default, `state` fields should be reset on copy
            attrs['copy'] = attrs.get('copy', False)
        if attrs.get('compute'):
            # by default, computed fields are not stored, computed in superuser
            # mode if stored, not copied (unless stored and explicitly not
            # readonly), and readonly (unless inversible)
            attrs['store'] = store = attrs.get('store', False)
            attrs['compute_sudo'] = attrs.get('compute_sudo', store)
            if not (attrs['store'] and not attrs.get('readonly', True)):
                attrs['copy'] = attrs.get('copy', False)
            attrs['readonly'] = attrs.get('readonly', not attrs.get('inverse'))
        if attrs.get('related'):
            # by default, related fields are not stored, computed in superuser
            # mode, not copied and readonly
            attrs['store'] = attrs.get('store', False)
            attrs['compute_sudo'] = attrs.get('compute_sudo', True)
            attrs['copy'] = attrs.get('copy', False)
            attrs['readonly'] = attrs.get('readonly', True)
        if attrs.get('company_dependent'):
            # by default, company-dependent fields are not copied
            attrs['copy'] = attrs.get('copy', False)

        return attrs

    def _setup_attrs(self, model_class, name):
        """ Setup the field parameter attributes. """
        attrs = self._get_attrs(model_class, name)
        self._extra_keys = tuple(key for key in attrs if not hasattr(Field, key))
        for key, val in attrs.items():
            setattr(self, key, val)

    @lazy_property
    def column_type(self):
        """ Return the column type and format for this field. """
        if self.company_dependent:
            return ('jsonb', 'jsonb')
        return self._column_type

    @lazy_property
    def column_format(self):
        """ Return the column format for this field. """
        return self.column_type[1] if self.column_type else None

    @lazy_property
    def column_cast_from(self):
        """ Return the column types from which this field can be cast. """
        return ()

    def new(self, **kwargs):
        """ Return a field like ``self`` with the given parameters ``kwargs``. """
        # determine the class to use for the new field
        if 'type' in kwargs:
            if kwargs['type'] != self.type:
                cls = MetaField.by_type.get(kwargs['type'], Field)
            else:
                cls = type(self)
        else:
            cls = type(self)

        # create the new field with a copy of self's parameters
        kwargs = dict(self._args__, **kwargs)
        kwargs['_base_fields'] = self._args__.get('_base_fields', ()) + (self,)
        field = cls(**kwargs)
        field._direct = False
        return field

    def prepare_setup(self):
        """ Prepare the field for setup by marking it as not setup. """
        self._setup_done = False

    def setup(self, model):
        """ Perform the complete setup of a field. """
        if not self._setup_done:
            # validate field params
            for key in self._extra_keys:
                if not model._valid_field_parameter(self, key):
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.warning(
                        "Field %s: unknown parameter %r, if this is an actual"
                        " parameter you may want to override the method"
                        " _valid_field_parameter on the relevant model in order to"
                        " allow it",
                        self, key
                    )
            if self.related:
                self.setup_related(model)
            else:
                self.setup_nonrelated(model)

            if not isinstance(self.required, bool):
                import warnings
                warnings.warn(f'Property {self}.required should be a boolean ({self.required}).')

            if not isinstance(self.readonly, bool):
                import warnings
                warnings.warn(f'Property {self}.readonly should be a boolean ({self.readonly}).')

            self._setup_done = True

    def setup_nonrelated(self, model):
        """ Determine the dependencies and inverse field(s) of ``self``. """
        pass

    def get_depends(self, model):
        """ Return the field's dependencies and cache dependencies. """
        # Import here to avoid circular imports
        from .utils import resolve_mro

        if self._depends is not None:
            # the parameter 'depends' has priority over 'depends' on compute
            return self._depends, self._depends_context or ()

        if self.related:
            if self._depends_context is not None:
                depends_context = self._depends_context
            else:
                related_model = model.env[self.related_field.model_name]
                depends, depends_context = self.related_field.get_depends(related_model)
            return [self.related], depends_context

        if not self.compute:
            return (), self._depends_context or ()

        # determine the functions implementing self.compute
        if isinstance(self.compute, str):
            funcs = resolve_mro(model, self.compute, callable)
        else:
            funcs = [self.compute]

        # collect depends and depends_context
        depends = []
        depends_context = list(self._depends_context or ())
        for func in funcs:
            deps = getattr(func, '_depends', ())
            depends.extend(deps(model) if callable(deps) else deps)
            depends_context.extend(getattr(func, '_depends_context', ()))

        # display_name may depend on context['lang'] (`test_lp1071710`)
        if self.automatic and self.name == 'display_name' and model._rec_name:
            if model._fields[model._rec_name].base_field.translate:
                if 'lang' not in depends_context:
                    depends_context.append('lang')

        return depends, depends_context

    def setup_related(self, model):
        """ Setup the attributes of a related field. """
        # Import here to avoid circular imports
        from .utils import first
        import logging
        _logger = logging.getLogger(__name__)

        assert isinstance(self.related, str), self.related

        # determine the chain of fields, and make sure they are all set up
        model_name = self.model_name
        for name in self.related.split('.'):
            field = model.pool[model_name]._fields.get(name)
            if field is None:
                raise KeyError(
                    f"Field {name} referenced in related field definition {self} does not exist."
                )
            if not field._setup_done:
                field.setup(model.env[model_name])
            model_name = field.comodel_name

        self.related_field = field

        # check type consistency
        if self.type != field.type:
            raise TypeError("Type of related field %s is inconsistent with %s" % (self, field))

        # determine dependencies, compute, inverse, and search
        self.compute = self._compute_related
        if self.inherited or not (self.readonly or field.readonly):
            self.inverse = self._inverse_related
        if field._description_searchable:
            # allow searching on self only if the related field is searchable
            self.search = self._search_related

        # A readonly related field without an inverse method should not have a
        # default value, as it does not make sense.
        if self.default and self.readonly and not self.inverse:
            _logger.warning("Redundant default on %s", self)

        # copy attributes from field to self (string, help, etc.)
        for attr, prop in self.related_attrs:
            # check whether 'attr' is explicitly set on self (from its field
            # definition), and ignore its class-level value (only a default)
            if attr not in self.__dict__ and prop.startswith('_related_'):
                setattr(self, attr, getattr(field, prop))

        for attr in field._extra_keys:
            if not hasattr(self, attr) and model._valid_field_parameter(self, attr):
                setattr(self, attr, getattr(field, attr))

        # special cases of inherited fields
        if self.inherited:
            self.inherited_field = field
            if field.required:
                self.required = True
            # add modules from delegate and target fields; the first one ensures
            # that inherited fields introduced via an abstract model (_inherits
            # being on the abstract model) are assigned an XML id
            delegate_field = model._fields[self.related.split('.')[0]]
            self._modules = tuple({*self._modules, *delegate_field._modules, *field._modules})

        if self.store and self.translate:
            _logger.warning("Translated stored related field (%s) will not be computed correctly in all languages", self)

    def traverse_related(self, record):
        """ Traverse the fields of the related field `self` except for the last
        one, and return it as a pair `(last_record, last_field)`. """
        from .utils import first
        for name in self.related.split('.')[:-1]:
            record = first(record[name])
        return record, self.related_field

    def _compute_related(self, records):
        """ Compute the related field ``self`` on ``records``. """
        from .utils import first
        from ..exceptions import AccessError

        values = list(records)
        for name in self.related.split('.')[:-1]:
            try:
                values = [first(value[name]) for value in values]
            except AccessError as e:
                description = records.env['ir.model']._get(records._name).name
                env = records.env
                raise AccessError(env._(
                    "%(previous_message)s\n\nImplicitly accessed through '%(document_kind)s' (%(document_model)s).",
                    previous_message=e.args[0],
                    document_kind=description,
                    document_model=records._name,
                ))
        # assign final values to records
        for record, value in zip(records, values):
            record[self.name] = self._process_related(value[self.related_field.name], record.env)

    def _process_related(self, value, env):
        """No transformation by default, but allows override."""
        return value

    def _inverse_related(self, records):
        """ Inverse the related field ``self`` on ``records``. """
        # store record values, otherwise they may be lost by cache invalidation!
        record_value = {record: record[self.name] for record in records}
        for record in records:
            target, field = self.traverse_related(record)
            # update 'target' only if 'record' and 'target' are both real or
            # both new (see `test_base_objects.py`, `test_basic`)
            if target and bool(target.id) == bool(record.id):
                target[field.name] = record_value[record]

    def _search_related(self, records, operator, value):
        """ Determine the domain to search on field ``self``. """
        from .. import expression

        # This should never happen to avoid bypassing security checks
        # and should already be converted to (..., 'in', subquery)
        assert operator not in ('any', 'not any')

        # determine whether the related field can be null
        if isinstance(value, (list, tuple)):
            value_is_null = any(val is False or val is None for val in value)
        else:
            value_is_null = value is False or value is None

        can_be_null = (  # (..., '=', False) or (..., 'not in', [truthy vals])
            (operator not in expression.NEGATIVE_TERM_OPERATORS and value_is_null)
            or (operator in expression.NEGATIVE_TERM_OPERATORS and not value_is_null)
        )

        def make_domain(path, model):
            if '.' not in path:
                return [(path, operator, value)]

            prefix, suffix = path.split('.', 1)
            field = model._fields[prefix]
            comodel = model.env[field.comodel_name]

            domain = [(prefix, 'in', comodel._search(make_domain(suffix, comodel)))]
            if can_be_null and field.type == 'many2one' and not field.required:
                return expression.OR([domain, [(prefix, '=', False)]])

            return domain

        model = records.env[self.model_name].with_context(active_test=False)
        model = model.sudo(records.env.su or self.compute_sudo)

        return make_domain(self.related, model)

    # properties used by setup_related() to copy values from related field
    @property
    def _related_comodel_name(self):
        return self.comodel_name

    @property
    def _related_string(self):
        return self.string

    @property
    def _related_help(self):
        return self.help

    @property
    def _related_groups(self):
        return self.groups

    @property
    def _related_aggregator(self):
        return self.aggregator

    @property
    def base_field(self):
        """ Return the base field of an inherited field, or ``self``. """
        return self.inherited_field.base_field if self.inherited_field else self

    def get_company_dependent_fallback(self, records):
        """ Get fallback value for company-dependent fields. """
        assert self.company_dependent
        from ..models import SUPERUSER_ID
        fallback = records.env['ir.default'] \
            .with_user(SUPERUSER_ID) \
            .with_company(records.env.company) \
            ._get_model_defaults(records._name).get(self.name)
        fallback = self.convert_to_cache(fallback, records, validate=False)
        return self.convert_to_record(fallback, records)

    def resolve_depends(self, registry):
        """ Return the dependencies of `self` as a collection of field tuples. """
        Model0 = registry[self.model_name]

        for dotnames in registry.field_depends[self]:
            field_seq = []
            model_name = self.model_name
            check_precompute = self.precompute

            for fname in dotnames.split('.'):
                Model = registry[model_name]
                field = Model._fields[fname]
                field_seq.append(field)
                if field.type in ('one2many', 'many2many'):
                    check_precompute = False
                model_name = field.comodel_name

            yield tuple(field_seq), check_precompute

    def __get__(self, record, owner=None) -> T:
        """ return the value of field ``self`` on ``record`` """
        if record is None:
            return self         # the field is accessed through the owner class

        # only a single record may be accessed
        record.ensure_one()
        env = record.env

        # determine the value of self for record
        if self.compute and not (self.store and record.id):
            # non-stored computed field or new record: compute the value
            try:
                record._prefetch_field(self)
            except AccessError:
                # lack of access rights: retrieve the value in the cache only
                pass

        try:
            return record._cache[self]
        except KeyError:
            pass

        # cache miss, determine the value
        if self.store and record.id:
            # stored field: fetch from database
            record._fetch_field(self)
        elif self.compute:
            # computed field: compute the value
            record._prefetch_field(self)
        else:
            # non-stored field without value: use default value
            value = self.default(record) if callable(self.default) else self.default
            record._cache[self] = self.convert_to_cache(value, record)

        return record._cache[self]

    def __set__(self, record, value):
        """ set the value of field ``self`` on ``record`` """
        # only a single record may be assigned
        record.ensure_one()
        env = record.env

        if self.readonly and self.store and record.id:
            # readonly stored field: check access rights
            if not env.su and not record._is_readonly_field_writable(self):
                raise AccessError(
                    f"Field '{self}' is readonly and cannot be modified."
                )

        # convert and validate the value
        value = self.convert_to_cache(value, record)

        # update the cache and inverse fields
        record._cache[self] = value
        record._inverse_field(self)

    def convert_to_cache(self, value, record, validate=True):
        """ Convert ``value`` to the cache format. """
        return value

    def convert_to_record(self, value, record):
        """ Convert ``value`` from the cache format to the record format. """
        return value

    def convert_to_read(self, value, record, use_display_name=True):
        """ Convert ``value`` from the record format to the format returned by read(). """
        return self.convert_to_record(value, record)

    def convert_to_write(self, value, record):
        """ Convert ``value`` from any format to the format of write(). """
        cache_value = self.convert_to_cache(value, record, validate=False)
        record_value = self.convert_to_record(cache_value, record)
        return record_value

    def convert_to_column(self, value, record, values=None, validate=True):
        """ Convert ``value`` to the database column format. """
        return self.convert_to_cache(value, record, validate)

    def convert_to_column_insert(self, value, record, values=None, validate=True):
        """ Convert ``value`` from the ``write`` format to the SQL parameter
        format for INSERT queries. This method handles the case of fields that
        store multiple values (translated or company-dependent).
        """
        value = self.convert_to_column(value, record, values, validate)
        if not self.company_dependent:
            return value
        # company-dependent field: store value for current company only
        fallback = record.env['ir.default']._get_model_defaults(record._name).get(self.name)
        if value == self.convert_to_column(fallback, record):
            return None
        from ..tools.sql import PsycopgJson
        return PsycopgJson({record.env.company.id: value})

    def convert_to_column_update(self, value, record):
        """ Convert ``value`` from the ``to_flush`` format to the SQL parameter
        format for UPDATE queries. The ``to_flush`` format is the same as the
        cache format, except for translated fields (``{'lang_code': 'value', ...}``
        or ``None``) and company-dependent fields (``{company_id: value, ...}``).
        """
        if self.company_dependent:
            from ..tools.sql import PsycopgJson
            return PsycopgJson(value) if value else value
        return value

    def convert_to_record_multi(self, values, records):
        """ Convert a list of values from the cache format to the record format.
        Some field classes may override this method to add optimizations for
        batch processing.
        """
        # spare the method lookup overhead
        convert = self.convert_to_record
        return [convert(value, record) for value, record in zip(values, records)]

    def convert_to_export(self, value, record):
        """ Convert ``value`` to the export format. """
        if not value:
            return ''
        return self.convert_to_record(value, record)

    def convert_to_display_name(self, value, record):
        """ Convert ``value`` from the record format to a suitable display name. """
        return str(value) if value else False

    def determine_inverse(self, records):
        """ Given the value of ``self`` on ``records``, inverse the computation. """
        from .utils import determine
        determine(self.inverse, records)

    def determine_domain(self, records, operator, value):
        """ Return a domain representing a condition on ``self``. """
        from .utils import determine
        return determine(self.search, records, operator, value)

    def get_description(self, env, attributes=None):
        """ Return a dictionary that describes the field ``self``. """
        desc = {
            'type': self.type,
            'string': self._description_string(env),
            'help': self._description_help(env),
            'readonly': self.readonly,
            'required': self.required,
            'index': bool(self.index),
            'default': self.default,
            'store': self.store,
            'manual': self.manual,
            'depends': self._description_depends(env),
            'related': self.related,
            'company_dependent': self.company_dependent,
            'searchable': self._description_searchable,
            'sortable': self._description_sortable(env),
            'groupable': self._description_groupable(env),
            'aggregator': self._description_aggregator(env),
        }
        if attributes:
            desc = {key: val for key, val in desc.items() if key in attributes}
        return desc

    def _description_depends(self, env):
        return env.registry.field_depends[self]

    @property
    def _description_searchable(self):
        return bool(self.store or self.search)

    def _description_sortable(self, env):
        if self.column_type and self.store:  # shortcut
            return True
        if self.compute and not self.store:
            return False
        if self.type in ('one2many', 'many2many'):
            return False
        return True

    def _description_groupable(self, env):
        if self.column_type and self.store:  # shortcut
            return True
        if self.compute and not self.store:
            return False
        if self.type in ('one2many', 'many2many'):
            return False
        return True

    def _description_aggregator(self, env):
        if not self.aggregator or self.column_type and self.store:  # shortcut
            return self.aggregator
        if self.compute and not self.store:
            return None
        if self.type in ('one2many', 'many2many'):
            return None
        return self.aggregator

    def _description_string(self, env):
        if self.string and env.lang:
            return env._(self.string)
        return self.string

    def _description_help(self, env):
        if self.help and env.lang:
            return env._(self.help)
        return self.help

    def is_editable(self):
        """ Return whether the field can be editable in a view. """
        return not self.readonly

    def is_accessible(self, env):
        """ Return whether the field is accessible from the given environment. """
        if self.groups:
            return env.user.has_group(self.groups)
        return True

    @property
    def column_order(self):
        """ Prescribed column order in table. """
        from .. import sql
        return 0 if self.column_type is None else sql.SQL_ORDER_BY_TYPE[self.column_type[0]]

    def update_db(self, model, columns):
        """ Update the database schema to implement this field.

            :param model: an instance of the field's model
            :param columns: a dict mapping column names to their configuration in database
            :return: ``True`` if the field must be recomputed on existing rows
        """
        if not self.column_type:
            return

        column = columns.get(self.name)

        # create/update the column, not null constraint; the index will be
        # managed by registry.check_indexes()
        self.update_db_column(model, column)
        self.update_db_notnull(model, column)

        # optimization for computing simple related fields like 'foo_id.bar'
        if (
            not column
            and self.related and self.related.count('.') == 1
            and self.related_field.store and not self.related_field.compute
            and not (self.related_field.type == 'binary' and self.related_field.attachment)
            and self.related_field.type not in ('one2many', 'many2many')
        ):
            join_field = model._fields[self.related.split('.')[0]]
            if (
                join_field.type == 'many2one'
                and join_field.store and not join_field.compute
            ):
                model.pool.post_init(self.update_db_related, model)
                # discard the "classical" computation
                return False

        return not column

    def update_db_column(self, model, column):
        """ Create/update the column corresponding to ``self``.

            :param model: an instance of the field's model
            :param column: the column's configuration (dict) if it exists, or ``None``
        """
        from .. import sql
        if not column:
            # the column does not exist, create it
            sql.create_column(model._cr, model._table, self.name, self.column_type[1], self.string)
            return
        if column['udt_name'] == self.column_type[0]:
            return
        if column['is_nullable'] == 'NO':
            sql.drop_not_null(model._cr, model._table, self.name)
        self._convert_db_column(model, column)

    def _convert_db_column(self, model, column):
        """ Convert the given database column to the type of the field. """
        from .. import sql
        sql.convert_column(model._cr, model._table, self.name, self.column_type[1])

    def update_db_notnull(self, model, column):
        """ Add or remove the NOT NULL constraint on ``self``.

            :param model: an instance of the field's model
            :param column: the column's configuration (dict) if it exists, or ``None``
        """
        from .. import sql
        from .utils import apply_required

        has_notnull = column and column['is_nullable'] == 'NO'

        if not column or (self.required and not has_notnull):
            # the column is new or it becomes required; initialize its values
            if model._table_has_rows():
                model._init_column(self.name)

        if self.required and not has_notnull:
            # _init_column may delay computations in post-init phase
            @model.pool.post_init
            def add_not_null():
                # flush values before adding NOT NULL constraint
                model.flush_model([self.name])
                model.pool.post_constraint(apply_required, model, self.name)

        elif not self.required and has_notnull:
            sql.drop_not_null(model._cr, model._table, self.name)

    def update_db_related(self, model):
        """ Compute a stored related field directly in SQL. """
        from .. import sql
        from ..sql import SQL

        comodel = model.env[self.related_field.model_name]
        join_field, comodel_field = self.related.split('.')
        model.env.cr.execute(SQL(
            """ UPDATE %(model_table)s AS x
                SET %(model_field)s = y.%(comodel_field)s
                FROM %(comodel_table)s AS y
                WHERE x.%(join_field)s = y.id """,
            model_table=SQL.identifier(model._table),
            model_field=SQL.identifier(self.name),
            comodel_table=SQL.identifier(comodel._table),
            comodel_field=SQL.identifier(comodel_field),
            join_field=SQL.identifier(join_field),
        ))

    def read(self, records):
        """ Read the value of ``self`` on ``records``, and store it in cache. """
        if not self.column_type:
            raise NotImplementedError("Method read() undefined on %s" % self)

    def create(self, record_values):
        """ Write the value of ``self`` on the given records, which have just
        been created.

        :param record_values: a list of pairs ``(record, value)``, where
            ``value`` is in the format of method :meth:`BaseModel.write`
        """
        for record, value in record_values:
            self.write(record, value)

    def write(self, records, value):
        """ Write the value of ``self`` on ``records``. This method must update
        the cache and prepare database updates.

        :param records:
        :param value: a value in any format
        """
        # discard recomputation of self on records
        records.env.remove_to_compute(self, records)

        # update the cache, and discard the records that are not stored
        cache = records.env.cache
        for record in records:
            cache[record][self] = self.convert_to_cache(value, record)

        # update the database if the field is stored
        if self.store:
            records._write({self.name: value})

    def mapped(self, records):
        """ Return the values of ``self`` for ``records``, either as a list
        or as a recordset (if ``self`` is a relational field).
        """
        if self.relational:
            # return a recordset
            result = records.env[self.comodel_name]
            for record in records:
                result |= record[self.name]
            return result
        else:
            # return a list
            return [record[self.name] for record in records]

    def recompute(self, records):
        """ Process the pending computations of ``self`` on ``records``. This
        should be called only if ``self`` is computed and stored.
        """
        to_compute_ids = records.env.transaction.tocompute.get(self)
        if not to_compute_ids:
            return

        def apply_except_missing(func, records):
            """ Apply `func` on `records`, with a fallback ignoring non-existent records. """
            try:
                func(records)
            except Exception:  # MissingError
                existing = records.exists()
                if existing:
                    func(existing)
                # mark the field as computed on missing records, otherwise they
                # remain to compute forever, which may lead to an infinite loop
                missing = records - existing
                for f in records.pool.field_computed[self]:
                    records.env.remove_to_compute(f, missing)

        # determine the records to compute
        ids = [id_ for id_ in to_compute_ids if id_ in records._ids]
        recs = records.browse(ids)

        # compute the field on the records
        if recs:
            apply_except_missing(self.compute_value, recs)

    def compute_value(self, records):
        """ Invoke the compute method on ``records``; the results are in cache. """
        from .utils import determine

        env = records.env
        if self.compute_sudo:
            records = records.sudo()

        # determine the compute method
        compute = self.compute
        if isinstance(compute, str):
            compute = getattr(records, compute)

        # call the compute method
        try:
            compute(records)
        except Exception:
            # if the compute method fails, mark the field as computed
            for record in records:
                env.cache[record][self] = False
